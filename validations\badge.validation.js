const Joi = require("joi");
const { exists, unique } = require("./custom.validation");

const badgeId = Joi.string().required().external(exists("Badge", "badge_id"));

const BadgeValidation = {
  create: {
    body: Joi.object().keys({
      name: Joi.string().required().max(255).external(unique("Badge", "name")),
      status: Joi.boolean().optional().default(true),
      images: Joi.array().items(Joi.string().uri()).optional().allow(null),
      content: Joi.object().optional().allow(null),
      schema: Joi.string().required().max(255),
      key: Joi.string().required().max(255),
      format: Joi.string().valid('PNG', 'JPEG', 'JPG', 'WEBP', 'BMP', 'TIFF', 'PDF').required(),
    }),
  },

  update: {
    params: Joi.object().keys({
      badgeId,
    }),
    body: Joi.object()
      .keys({
        name: Joi.string().optional().max(255),
        status: Joi.boolean().optional().default(true),
        images: Joi.array().items(Joi.string().uri()).optional().allow(null),
        content: Joi.object().optional().allow(null),
        variables: Joi.array().optional().allow(null),
      }),
  },

  badge: {
    params: Joi.object().keys({
      badgeId,
    }),
  },

  index: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1).optional(),
      limit: Joi.number().integer().min(1).max(100).default(10).optional(),
      sortBy: Joi.string().valid("name", "status", "created_at", "updated_at").default("updated_at").optional(),
      sortOrder: Joi.string().valid("asc", "desc").default("desc").optional(),
      search: Joi.string().allow("").optional(),
    }),
  },

  delete: {
    params: Joi.object().keys({
      badgeId,
    }),
  },

  print: {
    body: Joi.object().keys({
      badge_id: Joi.string().required().external(exists("Badge", "badge_id")),
      instance_id: Joi.string().required(),
    }),
  },

  modelAttributes: {
    params: Joi.object().keys({
      modelName: Joi.string().required().max(255),
    }),
  },
};

module.exports = BadgeValidation;
